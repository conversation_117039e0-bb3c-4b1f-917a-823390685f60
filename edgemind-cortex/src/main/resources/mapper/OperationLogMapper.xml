<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.cortex.mapper.OperationLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zibbava.edgemind.cortex.entity.OperationLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="operation_type" property="operationType" jdbcType="VARCHAR"/>
        <result column="module" property="module" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="request_url" property="requestUrl" jdbcType="VARCHAR"/>
        <result column="request_method" property="requestMethod" jdbcType="VARCHAR"/>
        <result column="request_params" property="requestParams" jdbcType="LONGVARCHAR"/>
        <result column="response_result" property="responseResult" jdbcType="LONGVARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="execution_time" property="executionTime" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询操作日志 -->
    <select id="selectLogPage" resultMap="BaseResultMap">
        SELECT 
            id,
            user_id,
            username,
            operation_type,
            module,
            description,
            request_url,
            request_method,
            request_params,
            response_result,
            ip_address,
            user_agent,
            execution_time,
            status,
            error_message,
            create_time
        FROM sys_operation_log
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="operationType != null and operationType != ''">
                AND operation_type = #{operationType}
            </if>
            <if test="module != null and module != ''">
                AND module LIKE CONCAT('%', #{module}, '%')
            </if>
            <if test="startTime != null">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
