-- RBAC系统初始数据脚本

-- 设置字符集
SET NAMES utf8mb4;

-- 清空现有数据（注意：这会删除所有数据）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE `sys_role_permission`;
TRUNCATE TABLE `sys_user_role`;
TRUNCATE TABLE `sys_operation_log`;
TRUNCATE TABLE `sys_login_log`;
TRUNCATE TABLE `sys_data_permission`;
DELETE FROM `sys_user` WHERE id > 0;
DELETE FROM `sys_role` WHERE id > 0;
DELETE FROM `sys_permission` WHERE id > 0;
DELETE FROM `sys_department` WHERE id > 0;
DELETE FROM `sys_settings` WHERE id > 0;
SET FOREIGN_KEY_CHECKS = 1;

-- 重置自增ID
ALTER TABLE `sys_user` AUTO_INCREMENT = 1;
ALTER TABLE `sys_role` AUTO_INCREMENT = 1;
ALTER TABLE `sys_permission` AUTO_INCREMENT = 1;
ALTER TABLE `sys_department` AUTO_INCREMENT = 1;
ALTER TABLE `sys_settings` AUTO_INCREMENT = 1;

-- ================================
-- 1. 初始化部门数据
-- ================================
INSERT INTO `sys_department` (`id`, `dept_name`, `dept_code`, `parent_id`, `sort_order`, `description`, `status`) VALUES
(1, '总公司', 'ROOT', 0, 1, '总公司', 1),
(2, '技术部', 'TECH', 1, 1, '技术研发部门', 1),
(3, '产品部', 'PRODUCT', 1, 2, '产品管理部门', 1),
(4, '运营部', 'OPERATION', 1, 3, '运营管理部门', 1),
(5, '人事部', 'HR', 1, 4, '人力资源部门', 1),
(6, '财务部', 'FINANCE', 1, 5, '财务管理部门', 1),
(7, '前端组', 'FRONTEND', 2, 1, '前端开发组', 1),
(8, '后端组', 'BACKEND', 2, 2, '后端开发组', 1),
(9, '测试组', 'QA', 2, 3, '质量保证组', 1);

-- ================================
-- 2. 初始化角色数据
-- ================================
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `data_scope`, `sort_order`, `status`) VALUES
(1, '超级管理员', 'ROLE_SUPER_ADMIN', '系统超级管理员，拥有所有权限', 'ALL', 1, 1),
(2, '系统管理员', 'ROLE_ADMIN', '系统管理员，拥有大部分管理权限', 'ALL', 2, 1),
(3, '部门管理员', 'ROLE_DEPT_ADMIN', '部门管理员，管理本部门及子部门', 'DEPT_AND_CHILD', 3, 1),
(4, '普通用户', 'ROLE_USER', '普通用户，基础功能权限', 'SELF', 4, 1),
(5, '访客用户', 'ROLE_GUEST', '访客用户，只读权限', 'SELF', 5, 1);

-- ================================
-- 3. 初始化权限数据
-- ================================
-- 一级菜单
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `icon`, `sort_order`, `status`) VALUES
(1, '系统管理', 'system', 'MENU', 0, '/system', 'bi-gear', 1, 1),
(2, '知识库管理', 'knowledge', 'MENU', 0, '/knowledge', 'bi-book', 2, 1),
(3, 'AI对话', 'chat', 'MENU', 0, '/chat', 'bi-chat-dots', 3, 1),
(4, '权限管理', 'permission', 'MENU', 1, '/system/permission', 'bi-shield-check', 1, 1),
(5, '系统监控', 'monitor', 'MENU', 1, '/system/monitor', 'bi-activity', 2, 1),
(6, '系统设置', 'settings', 'MENU', 1, '/system/settings', 'bi-sliders', 3, 1);

-- 权限管理子菜单
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `icon`, `sort_order`, `status`) VALUES
(10, '用户管理', 'user:manage', 'MENU', 4, '/system/permission/users', 'bi-people', 1, 1),
(11, '角色管理', 'role:manage', 'MENU', 4, '/system/permission/roles', 'bi-person-badge', 2, 1),
(12, '权限管理', 'permission:manage', 'MENU', 4, '/system/permission/permissions', 'bi-key', 3, 1),
(13, '部门管理', 'dept:manage', 'MENU', 4, '/system/permission/departments', 'bi-diagram-3', 4, 1);

-- 系统监控子菜单
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `icon`, `sort_order`, `status`) VALUES
(20, '在线用户', 'online:user', 'MENU', 5, '/system/monitor/online-users', 'bi-person-check', 1, 1),
(21, '操作日志', 'log:operation', 'MENU', 5, '/system/monitor/operation-logs', 'bi-journal-text', 2, 1),
(22, '登录日志', 'log:login', 'MENU', 5, '/system/monitor/login-logs', 'bi-door-open', 3, 1),
(23, '系统信息', 'system:info', 'MENU', 5, '/system/monitor/system-info', 'bi-info-circle', 4, 1);

-- 用户管理按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(100, '用户查询', 'user:manage:list', 'BUTTON', 10, 1, 1),
(101, '用户新增', 'user:manage:create', 'BUTTON', 10, 2, 1),
(102, '用户修改', 'user:manage:update', 'BUTTON', 10, 3, 1),
(103, '用户删除', 'user:manage:delete', 'BUTTON', 10, 4, 1),
(104, '重置密码', 'user:manage:reset-password', 'BUTTON', 10, 5, 1),
(105, '分配角色', 'user:manage:assign-role', 'BUTTON', 10, 6, 1),
(106, '用户导出', 'user:manage:export', 'BUTTON', 10, 7, 1),
(107, '用户导入', 'user:manage:import', 'BUTTON', 10, 8, 1);

-- 角色管理按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(110, '角色查询', 'role:manage:list', 'BUTTON', 11, 1, 1),
(111, '角色新增', 'role:manage:create', 'BUTTON', 11, 2, 1),
(112, '角色修改', 'role:manage:update', 'BUTTON', 11, 3, 1),
(113, '角色删除', 'role:manage:delete', 'BUTTON', 11, 4, 1),
(114, '分配权限', 'role:manage:assign-permission', 'BUTTON', 11, 5, 1),
(115, '数据权限', 'role:manage:data-permission', 'BUTTON', 11, 6, 1);

-- 权限管理按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(120, '权限查询', 'permission:manage:list', 'BUTTON', 12, 1, 1),
(121, '权限新增', 'permission:manage:create', 'BUTTON', 12, 2, 1),
(122, '权限修改', 'permission:manage:update', 'BUTTON', 12, 3, 1),
(123, '权限删除', 'permission:manage:delete', 'BUTTON', 12, 4, 1);

-- 部门管理按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(130, '部门查询', 'dept:manage:list', 'BUTTON', 13, 1, 1),
(131, '部门新增', 'dept:manage:create', 'BUTTON', 13, 2, 1),
(132, '部门修改', 'dept:manage:update', 'BUTTON', 13, 3, 1),
(133, '部门删除', 'dept:manage:delete', 'BUTTON', 13, 4, 1);

-- 在线用户按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(200, '在线用户查询', 'online:user:list', 'BUTTON', 20, 1, 1),
(201, '在线用户统计', 'online:user:statistics', 'BUTTON', 20, 2, 1),
(202, '强制下线', 'online:user:kickout', 'BUTTON', 20, 3, 1),
(203, '在线用户管理', 'online:user:manage', 'BUTTON', 20, 4, 1),
(204, '在线用户更新', 'online:user:update', 'BUTTON', 20, 5, 1);

-- 操作日志按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(210, '日志查询', 'log:operation:list', 'BUTTON', 21, 1, 1),
(211, '日志统计', 'log:operation:statistics', 'BUTTON', 21, 2, 1),
(212, '日志导出', 'log:operation:export', 'BUTTON', 21, 3, 1),
(213, '日志删除', 'log:operation:delete', 'BUTTON', 21, 4, 1);

-- 系统设置按钮权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `sort_order`, `status`) VALUES
(220, '设置查询', 'system:setting:list', 'BUTTON', 6, 1, 1),
(221, '设置修改', 'system:setting:update', 'BUTTON', 6, 2, 1),
(222, '设置新增', 'system:setting:create', 'BUTTON', 6, 3, 1),
(223, '设置删除', 'system:setting:delete', 'BUTTON', 6, 4, 1);

-- 知识库权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `sort_order`, `status`) VALUES
(300, '知识库查询', 'knowledge:list', 'BUTTON', 2, '/api/knowledge/list', 1, 1),
(301, '知识库上传', 'knowledge:upload', 'BUTTON', 2, '/api/knowledge/upload', 2, 1),
(302, '知识库删除', 'knowledge:delete', 'BUTTON', 2, '/api/knowledge/delete', 3, 1),
(303, '知识库管理', 'knowledge:manage', 'BUTTON', 2, '/api/knowledge/manage', 4, 1);

-- AI对话权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `sort_order`, `status`) VALUES
(400, 'AI对话', 'chat:use', 'BUTTON', 3, '/api/chat', 1, 1),
(401, '对话历史', 'chat:history', 'BUTTON', 3, '/api/chat/history', 2, 1),
(402, '对话管理', 'chat:manage', 'BUTTON', 3, '/api/chat/manage', 3, 1);

-- ================================
-- 4. 初始化用户数据
-- ================================
-- 密码都是 admin123 的MD5值：0192023a7bbd73250516f069df18b500
INSERT INTO `sys_user` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `dept_id`, `status`, `remark`) VALUES
(1, 'admin', '0192023a7bbd73250516f069df18b500', '超级管理员', '<EMAIL>', '13800138000', 1, 1, '系统超级管理员'),
(2, 'system', '0192023a7bbd73250516f069df18b500', '系统管理员', '<EMAIL>', '13800138001', 1, 1, '系统管理员'),
(3, 'tech_admin', '0192023a7bbd73250516f069df18b500', '技术部主管', '<EMAIL>', '13800138002', 2, 1, '技术部门管理员'),
(4, 'user', '0192023a7bbd73250516f069df18b500', '普通用户', '<EMAIL>', '13800138003', 2, 1, '普通用户'),
(5, 'guest', '0192023a7bbd73250516f069df18b500', '访客用户', '<EMAIL>', '13800138004', 2, 1, '访客用户');

-- ================================
-- 5. 初始化用户角色关联
-- ================================
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES
(1, 1), -- admin -> 超级管理员
(2, 2), -- system -> 系统管理员
(3, 3), -- tech_admin -> 部门管理员
(4, 4), -- user -> 普通用户
(5, 5); -- guest -> 访客用户

-- ================================
-- 6. 初始化角色权限关联（超级管理员拥有所有权限）
-- ================================
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`)
SELECT 1, id FROM `sys_permission` WHERE `status` = 1;

-- 系统管理员权限（除了用户删除）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`)
SELECT 2, id FROM `sys_permission`
WHERE `status` = 1 AND `permission_code` NOT IN ('user:manage:delete', 'role:manage:delete', 'permission:manage:delete');

-- 部门管理员权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) VALUES
(3, 10), (3, 100), (3, 101), (3, 102), (3, 105), -- 用户管理（不含删除）
(3, 13), (3, 130), (3, 131), (3, 132), -- 部门管理（不含删除）
(3, 20), (3, 200), (3, 201), -- 在线用户查询
(3, 21), (3, 210), (3, 211), -- 操作日志查询
(3, 2), (3, 300), (3, 301), (3, 302), (3, 303), -- 知识库管理
(3, 3), (3, 400), (3, 401), (3, 402); -- AI对话

-- 普通用户权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) VALUES
(4, 2), (4, 300), (4, 301), -- 知识库基础功能
(4, 3), (4, 400), (4, 401); -- AI对话基础功能

-- 访客用户权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) VALUES
(5, 2), (5, 300), -- 知识库查询
(5, 3), (5, 400); -- AI对话基础功能

-- ================================
-- 7. 初始化系统设置
-- ================================
INSERT INTO `sys_settings` (`setting_key`, `setting_value`, `description`, `setting_group`, `setting_type`, `is_system`, `sort_order`, `status`) VALUES
('system.name', 'EdgeMind智能知识管理系统', '系统名称', 'SYSTEM', 'STRING', 1, 1, 1),
('system.version', '1.0.0', '系统版本', 'SYSTEM', 'STRING', 1, 2, 1),
('system.logo', '/wkg/images/logo.png', '系统Logo', 'APPEARANCE', 'IMAGE', 1, 1, 1),
('system.favicon', '/wkg/images/favicon.ico', '网站图标', 'APPEARANCE', 'IMAGE', 1, 2, 1),
('security.password.min_length', '6', '密码最小长度', 'SECURITY', 'NUMBER', 1, 1, 1),
('security.password.require_special', 'false', '密码是否需要特殊字符', 'SECURITY', 'BOOLEAN', 1, 2, 1),
('security.login.max_attempts', '5', '登录最大尝试次数', 'SECURITY', 'NUMBER', 1, 3, 1),
('security.session.timeout', '7200', '会话超时时间（秒）', 'SECURITY', 'NUMBER', 1, 4, 1),
('log.operation.retention_days', '90', '操作日志保留天数', 'LOG', 'NUMBER', 1, 1, 1),
('log.operation.auto_cleanup', 'true', '是否自动清理过期日志', 'LOG', 'BOOLEAN', 1, 2, 1),
('notification.email.enabled', 'false', '是否启用邮件通知', 'NOTIFICATION', 'BOOLEAN', 1, 1, 1),
('notification.sms.enabled', 'false', '是否启用短信通知', 'NOTIFICATION', 'BOOLEAN', 1, 2, 1);

-- ================================
-- 8. 更新部门负责人
-- ================================
UPDATE `sys_department` SET `manager_id` = 1 WHERE `id` = 1; -- 总公司 -> admin
UPDATE `sys_department` SET `manager_id` = 3 WHERE `id` = 2; -- 技术部 -> tech_admin

-- ================================
-- 9. 初始化数据权限配置
-- ================================
INSERT INTO `sys_data_permission` (`role_id`, `permission_type`, `description`, `status`) VALUES
(1, 'ALL', '超级管理员拥有所有数据权限', 1),
(2, 'ALL', '系统管理员拥有所有数据权限', 1),
(3, 'DEPT_AND_CHILD', '部门管理员拥有本部门及子部门数据权限', 1),
(4, 'SELF', '普通用户只能查看自己的数据', 1),
(5, 'SELF', '访客用户只能查看自己的数据', 1);

-- 提交事务
COMMIT;
