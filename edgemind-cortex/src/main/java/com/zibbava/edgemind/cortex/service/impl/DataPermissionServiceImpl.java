package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.entity.DataPermission;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.mapper.DataPermissionMapper;
import com.zibbava.edgemind.cortex.service.DataPermissionService;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import com.zibbava.edgemind.cortex.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据权限服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataPermissionServiceImpl extends ServiceImpl<DataPermissionMapper, DataPermission> implements DataPermissionService {

    private final DataPermissionMapper dataPermissionMapper;
    private final UserService userService;
    private final DepartmentService departmentService;
    private final ObjectMapper objectMapper;

    @Override
    public List<DataPermission> getDataPermissionsByPermissionId(Long permissionId) {
        return dataPermissionMapper.selectByPermissionId(permissionId);
    }

    @Override
    public List<DataPermission> getDataPermissionsByRoleId(Long roleId) {
        return dataPermissionMapper.selectByRoleId(roleId);
    }

    @Override
    public List<DataPermission> getDataPermissionsByUserId(Long userId) {
        return dataPermissionMapper.selectByUserId(userId);
    }

    @Override
    @Transactional
    public DataPermission configureDataPermission(Long permissionId, DataPermission.DataScope dataScope, List<Long> deptIds, String filterSql) {
        // 删除现有配置
        removeDataPermissionByPermissionId(permissionId);
        
        // 创建新配置
        DataPermission dataPermission = new DataPermission();
        dataPermission.setPermissionId(permissionId);
        dataPermission.setDataScope(dataScope);
        
        // 处理部门ID列表
        if (!CollectionUtils.isEmpty(deptIds)) {
            try {
                dataPermission.setDeptIds(objectMapper.writeValueAsString(deptIds));
            } catch (Exception e) {
                log.error("序列化部门ID列表失败", e);
                dataPermission.setDeptIds("[]");
            }
        }
        
        // 设置过滤SQL
        if (StringUtils.hasText(filterSql)) {
            dataPermission.setFilterSql(filterSql);
        }
        
        this.save(dataPermission);
        return dataPermission;
    }

    @Override
    @Transactional
    public void removeDataPermissionByPermissionId(Long permissionId) {
        LambdaQueryWrapper<DataPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataPermission::getPermissionId, permissionId);
        this.remove(wrapper);
    }

    @Override
    public String buildDataPermissionSql(Long userId, String resourceType) {
        List<DataPermission> dataPermissions = getDataPermissionsByUserId(userId);
        
        if (CollectionUtils.isEmpty(dataPermissions)) {
            return ""; // 无数据权限限制
        }
        
        List<String> conditions = new ArrayList<>();
        
        for (DataPermission dp : dataPermissions) {
            String condition = buildConditionByDataScope(userId, dp, resourceType);
            if (StringUtils.hasText(condition)) {
                conditions.add(condition);
            }
        }
        
        if (conditions.isEmpty()) {
            return "";
        }
        
        return " AND (" + String.join(" OR ", conditions) + ")";
    }

    @Override
    public List<Long> getAccessibleDeptIds(Long userId) {
        List<DataPermission> dataPermissions = getDataPermissionsByUserId(userId);
        List<Long> accessibleDeptIds = new ArrayList<>();
        
        User user = userService.getById(userId);
        if (user == null) {
            return accessibleDeptIds;
        }
        
        for (DataPermission dp : dataPermissions) {
            switch (dp.getDataScope()) {
                case ALL:
                    // 返回所有部门ID
                    return departmentService.list().stream()
                            .map(dept -> dept.getId())
                            .collect(Collectors.toList());
                            
                case DEPT:
                    // 仅本部门
                    if (user.getDeptId() != null) {
                        accessibleDeptIds.add(user.getDeptId());
                    }
                    break;
                    
                case DEPT_AND_SUB:
                    // 本部门及子部门
                    if (user.getDeptId() != null) {
                        accessibleDeptIds.add(user.getDeptId());
                        List<Long> subDeptIds = departmentService.getDescendantDeptIds(user.getDeptId());
                        accessibleDeptIds.addAll(subDeptIds);
                    }
                    break;
                    
                case SELF:
                    // 仅本人，不涉及部门
                    break;
                    
                case CUSTOM:
                    // 自定义部门
                    List<Long> customDeptIds = parseDeptIds(dp.getDeptIds());
                    accessibleDeptIds.addAll(customDeptIds);
                    break;
            }
        }
        
        return accessibleDeptIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public boolean canAccessDeptData(Long userId, Long deptId) {
        List<Long> accessibleDeptIds = getAccessibleDeptIds(userId);
        return accessibleDeptIds.contains(deptId);
    }

    /**
     * 根据数据范围构建条件
     */
    private String buildConditionByDataScope(Long userId, DataPermission dataPermission, String resourceType) {
        User user = userService.getById(userId);
        if (user == null) {
            return "1=0"; // 用户不存在，无权限
        }
        
        switch (dataPermission.getDataScope()) {
            case ALL:
                return "1=1"; // 全部数据
                
            case DEPT:
                if (user.getDeptId() != null) {
                    return String.format("dept_id = %d", user.getDeptId());
                }
                break;
                
            case DEPT_AND_SUB:
                if (user.getDeptId() != null) {
                    List<Long> deptIds = new ArrayList<>();
                    deptIds.add(user.getDeptId());
                    deptIds.addAll(departmentService.getDescendantDeptIds(user.getDeptId()));
                    
                    String deptIdStr = deptIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    return String.format("dept_id IN (%s)", deptIdStr);
                }
                break;
                
            case SELF:
                return String.format("create_user_id = %d", userId);
                
            case CUSTOM:
                if (StringUtils.hasText(dataPermission.getFilterSql())) {
                    return dataPermission.getFilterSql();
                } else {
                    List<Long> deptIds = parseDeptIds(dataPermission.getDeptIds());
                    if (!deptIds.isEmpty()) {
                        String deptIdStr = deptIds.stream()
                                .map(String::valueOf)
                                .collect(Collectors.joining(","));
                        return String.format("dept_id IN (%s)", deptIdStr);
                    }
                }
                break;
        }
        
        return "1=0"; // 默认无权限
    }

    /**
     * 解析部门ID列表
     */
    private List<Long> parseDeptIds(String deptIdsJson) {
        if (!StringUtils.hasText(deptIdsJson)) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(deptIdsJson, new TypeReference<List<Long>>() {});
        } catch (Exception e) {
            log.error("解析部门ID列表失败: {}", deptIdsJson, e);
            return new ArrayList<>();
        }
    }
}
